<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash; // Or Str::random() for password
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Http;

class GoogleLoginController extends Controller
{
    public function redirectToGoogle()
    {
        try {
            return Socialite::driver('google')
                ->redirectUrl(url('/auth/callback/google'))
                ->scopes([
                    'openid',
                    'email',
                    'profile',
                    'https://www.googleapis.com/auth/user.birthday.read',
                    'https://www.googleapis.com/auth/user.gender.read',
                    'https://www.googleapis.com/auth/user.phonenumbers.read'
                ])
                ->redirect();
        } catch (\Exception $e) {
            return redirect('/login')->with('error', 'Unable to connect to Google. Please try again.');
        }
    }

    public function handleGoogleCallback()
    {
        try {
            \Log::info('Google OAuth callback received');

            $googleUser = Socialite::driver('google')
                ->redirectUrl(url('/auth/callback/google'))
                ->scopes([
                    'openid',
                    'email',
                    'profile',
                    'https://www.googleapis.com/auth/user.birthday.read',
                    'https://www.googleapis.com/auth/user.gender.read',
                    'https://www.googleapis.com/auth/user.phonenumbers.read'
                ])
                ->user();

            // ===== DEBUG: Display ALL Google User Information =====
            echo "<h1>Google OAuth Debug Information</h1>";
            echo "<h2>Basic User Data:</h2>";
            echo "<pre>";
            echo "ID: " . $googleUser->getId() . "\n";
            echo "Name: " . $googleUser->getName() . "\n";
            echo "Email: " . $googleUser->getEmail() . "\n";
            echo "Avatar: " . $googleUser->getAvatar() . "\n";
            echo "Nickname: " . $googleUser->getNickname() . "\n";
            echo "</pre>";

            echo "<h2>Raw User Object (all properties):</h2>";
            echo "<pre>";
            var_dump($googleUser);
            echo "</pre>";

            echo "<h2>User Token Information:</h2>";
            echo "<pre>";
            echo "Token: " . $googleUser->token . "\n";
            echo "Refresh Token: " . ($googleUser->refreshToken ?? 'Not available') . "\n";
            echo "Expires In: " . ($googleUser->expiresIn ?? 'Not available') . "\n";
            echo "</pre>";

            echo "<h2>Raw User Data Array:</h2>";
            echo "<pre>";
            print_r($googleUser->getRaw());
            echo "</pre>";

            echo "<h2>🎯 SPECIFIC DATA WE'RE LOOKING FOR:</h2>";
            echo "<pre>";
            $rawData = $googleUser->getRaw();

            // Look for birthday/date of birth
            echo "=== BIRTHDAY/DATE OF BIRTH ===\n";
            if (isset($rawData['birthday'])) {
                echo "Birthday: ";
                print_r($rawData['birthday']);
            }
            if (isset($rawData['birthdate'])) {
                echo "Birthdate: ";
                print_r($rawData['birthdate']);
            }
            if (isset($rawData['date_of_birth'])) {
                echo "Date of Birth: ";
                print_r($rawData['date_of_birth']);
            }
            if (isset($rawData['birthdays'])) {
                echo "Birthdays: ";
                print_r($rawData['birthdays']);
            }

            // Look for gender
            echo "\n=== GENDER ===\n";
            if (isset($rawData['gender'])) {
                echo "Gender: ";
                print_r($rawData['gender']);
            }
            if (isset($rawData['genders'])) {
                echo "Genders: ";
                print_r($rawData['genders']);
            }

            // Look for phone numbers
            echo "\n=== PHONE NUMBERS ===\n";
            if (isset($rawData['phone'])) {
                echo "Phone: ";
                print_r($rawData['phone']);
            }
            if (isset($rawData['phone_number'])) {
                echo "Phone Number: ";
                print_r($rawData['phone_number']);
            }
            if (isset($rawData['phoneNumbers'])) {
                echo "Phone Numbers: ";
                print_r($rawData['phoneNumbers']);
            }
            if (isset($rawData['phone_numbers'])) {
                echo "Phone Numbers: ";
                print_r($rawData['phone_numbers']);
            }

            echo "\n=== ALL OTHER ATTRIBUTES ===\n";
            foreach ($rawData as $key => $value) {
                // Skip the ones we already displayed above
                if (!in_array($key, ['birthday', 'birthdate', 'date_of_birth', 'birthdays', 'gender', 'genders', 'phone', 'phone_number', 'phoneNumbers', 'phone_numbers'])) {
                    echo "$key: ";
                    if (is_array($value) || is_object($value)) {
                        print_r($value);
                    } else {
                        echo $value . "\n";
                    }
                }
            }
            echo "</pre>";

            echo "<h2>Additional Methods (if available):</h2>";
            echo "<pre>";
            $methods = get_class_methods($googleUser);
            echo "Available methods:\n";
            foreach ($methods as $method) {
                echo "- $method\n";
            }
            echo "</pre>";

            echo "<h2>Object Properties:</h2>";
            echo "<pre>";
            $reflection = new \ReflectionObject($googleUser);
            $properties = $reflection->getProperties();
            foreach ($properties as $property) {
                $property->setAccessible(true);
                $value = $property->getValue($googleUser);
                echo $property->getName() . ": ";
                if (is_array($value) || is_object($value)) {
                    print_r($value);
                } else {
                    echo $value . "\n";
                }
            }
            echo "</pre>";

            echo "<h2>Scopes Requested:</h2>";
            echo "<pre>";
            echo "Scopes:\n";
            echo "- openid\n";
            echo "- email\n";
            echo "- profile\n";
            echo "- https://www.googleapis.com/auth/user.birthday.read\n";
            echo "- https://www.googleapis.com/auth/user.gender.read\n";
            echo "- https://www.googleapis.com/auth/user.phonenumbers.read\n";
            echo "Redirect URL: " . url('/auth/callback/google') . "\n";
            echo "</pre>";

            echo "<h2>Session Data:</h2>";
            echo "<pre>";
            print_r(session()->all());
            echo "</pre>";

            echo "<h2>Request Data:</h2>";
            echo "<pre>";
            print_r(request()->all());
            echo "</pre>";

            // ===== FETCH ADDITIONAL DATA FROM PEOPLE API =====
            echo "<h2>🔍 FETCHING ADDITIONAL DATA FROM PEOPLE API:</h2>";
            echo "<pre>";

            try {
                $additionalData = $this->fetchAdditionalGoogleData($googleUser->token);
                echo "Additional data fetched successfully:\n";
                print_r($additionalData);
            } catch (\Exception $e) {
                echo "Error fetching additional data: " . $e->getMessage() . "\n";
                echo "Stack trace: " . $e->getTraceAsString() . "\n";
            }

            echo "</pre>";

            echo "<hr><p><strong>Debug complete. Exiting...</strong></p>";
            exit(); // Exit here to prevent further execution

            // Original code below (commented out for debugging)
            /*
            \Log::info('Google user data received: ' . $googleUser->getEmail());

            // Check if user already exists
            $existingUser = User::where('google_id', $googleUser->getId())->first();
            $isNewUser = !$existingUser;

            // Find user by google_id or create new one
            $user = User::updateOrCreate([
                'google_id' => $googleUser->getId(),
            ], [
                'name' => $googleUser->getName(),
                'email' => $googleUser->getEmail(),
                'password' => Hash::make(uniqid()) // Password is required but not used
            ]);

            // For new users only, enable Time Spending Service by default
            if ($isNewUser) {
                $user->update([
                    'is_time_spending_enabled' => true
                ]);
                \Log::info('Time Spending Service enabled by default for new user: ' . $user->email);
            }

            Auth::login($user, true); // Log the user in

            \Log::info('User logged in successfully: ' . $user->email);

            // Redirect to home page - the root route will handle profile completion check
            return redirect('/')->with('success', 'Welcome back! You have successfully logged in.');
            */

        } catch (\Exception $e) {
            echo "<h1>Google OAuth Error</h1>";
            echo "<pre>";
            echo "Error Message: " . $e->getMessage() . "\n";
            echo "Error File: " . $e->getFile() . "\n";
            echo "Error Line: " . $e->getLine() . "\n";
            echo "Stack Trace:\n" . $e->getTraceAsString();
            echo "</pre>";
            exit();
        }
    }

    /**
     * Fetch additional user data from Google People API
     */
    private function fetchAdditionalGoogleData($accessToken)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $accessToken,
            'Accept' => 'application/json',
        ])->get('https://people.googleapis.com/v1/people/me', [
            'personFields' => 'birthdays,genders,phoneNumbers,emailAddresses,names,photos'
        ]);

        if ($response->successful()) {
            return $response->json();
        } else {
            throw new \Exception('Failed to fetch additional data: ' . $response->body());
        }
    }
}
